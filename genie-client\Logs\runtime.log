2025-08-02 14:14:26 - root - INFO - [logger.py:89] - 日志器 'None' 初始化完成，级别: DEBUG
2025-08-02 14:14:26 - asyncio - DEBUG - [proactor_events.py:629] - Using proactor: IocpProactor
2025-08-02 15:04:10 - root - INFO - [logger.py:89] - 日志器 'None' 初始化完成，级别: DEBUG
2025-08-02 15:04:10 - asyncio - DEBUG - [proactor_events.py:629] - Using proactor: IocpProactor
2025-08-02 15:05:53 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 15:05:53 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 15:05:53 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 15:05:53 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 15:05:53 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 15:05:53 - root - INFO - [client.py:117] - [2841923940560] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:05:53 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:05:54 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 15:05:54 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF42F90>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:05:54 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF76720> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 15:05:54 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295ADD0C510>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 07:05:55 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820617541183548096275eb93789b4c028374b6d6f8478b50982e8c6be;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820617541183548096275eb93789b4c028374b6d6f8478b50982e8c6be;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'd4466aa8-9f28-4d10-b6ad-b17b15c67667'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:05:55 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 15:05:55 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 15:05:55 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 15:05:55 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=c4daca0de30c4a598e0791c7abcca782
2025-08-02 15:05:55 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=c4daca0de30c4a598e0791c7abcca782
2025-08-02 15:05:55 - root - DEBUG - [client.py:129] - [2841923940560] SSE流连接已建立
2025-08-02 15:05:55 - root - DEBUG - [client.py:134] - [2841923940560] 客户端会话已创建
2025-08-02 15:05:55 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 15:05:55 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 15:05:55 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF66090>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:05:55 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF76720> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 15:05:55 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFED8DD0>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:05:56 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'fe9169eb-3bc4-48a2-8f85-8b29140da9c5'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:05:56 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=c4daca0de30c4a598e0791c7abcca782 "HTTP/1.1 202 Accepted"
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:05:56 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:05:56 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:05:56 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 15:05:56 - root - INFO - [client.py:138] - [2841923940560] SSE连接建立成功
2025-08-02 15:05:56 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 15:05:56 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:05:57 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'f16ad518-e804-4316-86ec-4842408ae66e'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:05:57 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=c4daca0de30c4a598e0791c7abcca782 "HTTP/1.1 202 Accepted"
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:05:57 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:05:57 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:05:57 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'b24fac6d-7085-40b2-83d6-7fdf2e72110c'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:05:57 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=c4daca0de30c4a598e0791c7abcca782 "HTTP/1.1 202 Accepted"
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:05:57 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:05:57 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:05:57 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 15:05:57 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 15:05:57 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 15:05:57 - root - DEBUG - [client.py:214] - [2841923940560] 开始清理SSE连接资源...
2025-08-02 15:05:57 - root - DEBUG - [client.py:219] - [2841923940560] 会话上下文已清理
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295afe58290')
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:05:57 - root - DEBUG - [client.py:228] - [2841923940560] 上下文已清理
2025-08-02 15:05:57 - root - INFO - [client.py:234] - [2841923940560] SSE连接资源清理完成
2025-08-02 15:12:00 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 15:12:00 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 15:12:00 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 15:12:00 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 15:12:00 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 15:12:00 - root - INFO - [client.py:117] - [2841924542672] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:12:00 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:12:00 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 15:12:00 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFE582D0>
2025-08-02 15:12:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:12:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:12:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:12:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:12:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:12:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:12:00 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF76F90> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 15:12:01 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF17650>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 07:12:01 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820217541187212256810e35e7f796ac74365b8a528501fa3f72e95f18;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820217541187212256810e35e7f796ac74365b8a528501fa3f72e95f18;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'7f885545-c5f1-4f3f-ba34-25ad3f6fa7eb'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:12:01 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 15:12:01 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 15:12:01 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 15:12:01 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=c69b76cc661a4d2e9eadf49fccc946f1
2025-08-02 15:12:01 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=c69b76cc661a4d2e9eadf49fccc946f1
2025-08-02 15:12:01 - root - DEBUG - [client.py:129] - [2841924542672] SSE流连接已建立
2025-08-02 15:12:01 - root - DEBUG - [client.py:134] - [2841924542672] 客户端会话已创建
2025-08-02 15:12:01 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 15:12:01 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 15:12:01 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFEC8390>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:12:01 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF76F90> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 15:12:02 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF43250>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:12:02 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:12:02 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:12:02 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'fff54ca8-b4a8-4912-b1bc-ac493e3f2dc0'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:12:02 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=c69b76cc661a4d2e9eadf49fccc946f1 "HTTP/1.1 202 Accepted"
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:12:02 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:12:02 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 15:12:02 - root - INFO - [client.py:138] - [2841924542672] SSE连接建立成功
2025-08-02 15:12:02 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:12:02 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'aae5b8d9-5dd5-45c1-8cea-fe4f5dbab3f5'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:12:02 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=c69b76cc661a4d2e9eadf49fccc946f1 "HTTP/1.1 202 Accepted"
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:12:02 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:12:02 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:12:03 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:12:03 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 15:12:03 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 15:12:03 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 15:12:03 - root - DEBUG - [client.py:214] - [2841924542672] 开始清理SSE连接资源...
2025-08-02 15:12:03 - root - DEBUG - [client.py:219] - [2841924542672] 会话上下文已清理
2025-08-02 15:12:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:12:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.failed exception=CancelledError('Cancelled by cancel scope 295afe4b990')
2025-08-02 15:12:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:12:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295afe4b990')
2025-08-02 15:12:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:12:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:12:03 - root - DEBUG - [client.py:228] - [2841924542672] 上下文已清理
2025-08-02 15:12:03 - root - INFO - [client.py:234] - [2841924542672] SSE连接资源清理完成
2025-08-02 15:17:18 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 15:17:18 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 15:17:18 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 15:17:18 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 15:17:18 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 15:17:18 - root - INFO - [client.py:117] - [2841924964304] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:17:18 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:17:18 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 15:17:18 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF08690>
2025-08-02 15:17:18 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:17:18 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:17:18 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:17:18 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:17:18 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:17:18 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:17:18 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77770> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 15:17:19 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF08F50>
2025-08-02 15:17:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 15:17:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:17:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 15:17:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:17:19 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 07:17:20 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117541190400126414e005f7507e4bdd0900c99dff9b597b8fe2a88;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117541190400126414e005f7507e4bdd0900c99dff9b597b8fe2a88;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'e4bc33d3-de79-4487-b17e-e56e67574146'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:17:20 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 15:17:20 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 15:17:20 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 15:17:20 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=7c1ad1d27a0747eb824a3668e5f6fdbb
2025-08-02 15:17:20 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=7c1ad1d27a0747eb824a3668e5f6fdbb
2025-08-02 15:17:20 - root - DEBUG - [client.py:129] - [2841924964304] SSE流连接已建立
2025-08-02 15:17:20 - root - DEBUG - [client.py:134] - [2841924964304] 客户端会话已创建
2025-08-02 15:17:20 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 15:17:20 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 15:17:20 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF0BD90>
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:17:20 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77770> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 15:17:21 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF08E90>
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:17:21 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'ae503f0d-d7ce-4762-9048-126c38d32a86'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:17:21 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=7c1ad1d27a0747eb824a3668e5f6fdbb "HTTP/1.1 202 Accepted"
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:17:21 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:17:22 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:17:22 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 15:17:22 - root - INFO - [client.py:138] - [2841924964304] SSE连接建立成功
2025-08-02 15:17:22 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 15:17:22 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:17:22 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'85856136-bc48-4289-9fa4-7ee993fc7b27'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:17:22 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=7c1ad1d27a0747eb824a3668e5f6fdbb "HTTP/1.1 202 Accepted"
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:17:22 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:17:22 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:17:23 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'62b137ca-7f98-4abe-abfa-1ecfb7801da1'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:17:23 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=7c1ad1d27a0747eb824a3668e5f6fdbb "HTTP/1.1 202 Accepted"
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:17:23 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:17:23 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:17:23 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 15:17:23 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 15:17:23 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 15:17:23 - root - DEBUG - [client.py:214] - [2841924964304] 开始清理SSE连接资源...
2025-08-02 15:17:23 - root - DEBUG - [client.py:219] - [2841924964304] 会话上下文已清理
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295aff11d10')
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:17:23 - root - DEBUG - [client.py:228] - [2841924964304] 上下文已清理
2025-08-02 15:17:23 - root - INFO - [client.py:234] - [2841924964304] SSE连接资源清理完成
