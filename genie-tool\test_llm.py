#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import os

from genie_tool.util.llm_util import ask_llm

async def test_llm():
    """测试LLM调用"""
    print("开始测试LLM调用...")
    
    try:
        messages = [
            {"role": "system", "content": "你是一个有用的助手。"},
            {"role": "user", "content": "请简单介绍一下Android技术栈。"}
        ]
        
        print("发送请求到智谱AI...")
        
        # 测试非流式调用
        async for response in ask_llm(
            messages=messages,
            model="glm-4.5",
            temperature=0.7,
            stream=False,
            only_content=True
        ):
            print("LLM响应:")
            print(response)
            break
            
    except Exception as e:
        print(f"LLM调用失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_llm())
