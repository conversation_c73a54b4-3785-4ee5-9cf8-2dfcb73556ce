2025-08-02 15:06:04.195 INFO log_util.__aenter__ ab954f73-3213-4af1-b77e-de85502aba16 POST /v1/tool/report start...
2025-08-02 15:06:04.197 INFO middleware_util.custom_route_handler ab954f73-3213-4af1-b77e-de85502aba16 POST /v1/tool/report body={"contentStream":true,"fileDescription":"Android技术栈发展方向、发展前景及与鸿蒙系统对比分析报告","fileName":"Android技术栈发展分析与鸿蒙对比报告.html","fileNames":[],"fileType":"html","query":"android技术主要技术栈发展方向，发展前景，与鸿蒙对比","requestId":"geniesession-1754118353275-106:1754118353312-481","stream":true,"streamMode":{"mode":"token","token":10},"task":"基于搜索结果，生成一份详细的HTML报告，包含以下内容：\n1. Android技术栈主要发展方向\n   - Jetpack组件化开发\n   - Kotlin语言发展\n   - 跨平台技术（Flutter、React Native等）\n   - AI/ML集成\n   - 5G和物联网支持\n   - 性能优化和安全性提升\n\n2. Android发展前景分析\n   - 市场份额和用户基础\n   - 开发生态系统\n   - 就业机会和薪资趋势\n   - 未来技术趋势\n\n3. Android与鸿蒙系统对比\n   - 技术架构对比\n   - 开发生态对比\n   - 市场定位对比\n   - 优缺点分析\n   - 发展前景对比\n\n报告需要结构清晰，内容详实，包含最新的技术趋势和市场分析。"}
2025-08-02 15:06:04.202 INFO log_util.__enter__ ab954f73-3213-4af1-b77e-de85502aba16 enter report start...
2025-08-02 15:06:04.202 INFO log_util.__exit__ ab954f73-3213-4af1-b77e-de85502aba16 enter report cost=[0 ms]
2025-08-02 15:06:04.203 INFO log_util.__enter__ ab954f73-3213-4af1-b77e-de85502aba16 enter html_report start...
2025-08-02 15:06:04.203 INFO log_util.__exit__ ab954f73-3213-4af1-b77e-de85502aba16 enter html_report cost=[0 ms]
2025-08-02 15:06:04.204 INFO log_util.__aenter__ ab954f73-3213-4af1-b77e-de85502aba16  download_all_files start...
2025-08-02 15:06:04.204 INFO log_util.__aexit__ ab954f73-3213-4af1-b77e-de85502aba16  download_all_files cost=[0 ms]
2025-08-02 15:06:04.205 INFO log_util.__enter__ ab954f73-3213-4af1-b77e-de85502aba16  truncate_files start...
2025-08-02 15:06:04.206 INFO log_util.__exit__ ab954f73-3213-4af1-b77e-de85502aba16  truncate_files cost=[1 ms]
2025-08-02 15:06:04.206 INFO log_util.__enter__ ab954f73-3213-4af1-b77e-de85502aba16  truncate_files start...
2025-08-02 15:06:04.207 INFO log_util.__exit__ ab954f73-3213-4af1-b77e-de85502aba16  truncate_files cost=[1 ms]
2025-08-02 15:06:04.209 INFO log_util.__aexit__ ab954f73-3213-4af1-b77e-de85502aba16 POST /v1/tool/report cost=[14 ms]
2025-08-02 15:12:15.444 INFO log_util.__aenter__ 135cfd50-1bd0-41b1-8126-3a498b7a1ede POST /v1/tool/report start...
2025-08-02 15:12:15.445 INFO middleware_util.custom_route_handler 135cfd50-1bd0-41b1-8126-3a498b7a1ede POST /v1/tool/report body={"contentStream":true,"fileDescription":"Android技术栈发展方向、发展前景及与鸿蒙系统对比分析报告","fileName":"Android技术栈发展分析与鸿蒙对比报告.html","fileNames":[],"fileType":"html","query":"android技术主要技术栈发展方向，发展前景，与鸿蒙对比\n","requestId":"geniesession-1754118720392-1451:1754118720412-8114","stream":true,"streamMode":{"mode":"token","token":10},"task":"基于搜索结果，生成一份关于Android技术栈主要发展方向、发展前景以及与鸿蒙系统对比的详细分析报告。报告应包含以下内容：\n1. Android技术栈的主要发展方向（包括技术趋势、架构演进、开发工具等）\n2. Android的发展前景（市场占有率、就业前景、技术生态等）\n3. Android与鸿蒙系统的详细对比（技术架构、开发模式、生态系统、市场表现等）\n4. 总结和建议\n\n报告需要结构清晰，内容详实，基于最新的技术发展趋势和市场数据。"}
2025-08-02 15:12:15.446 INFO log_util.__enter__ 135cfd50-1bd0-41b1-8126-3a498b7a1ede enter report start...
2025-08-02 15:12:15.446 INFO log_util.__exit__ 135cfd50-1bd0-41b1-8126-3a498b7a1ede enter report cost=[0 ms]
2025-08-02 15:12:15.448 INFO log_util.__enter__ 135cfd50-1bd0-41b1-8126-3a498b7a1ede enter html_report start...
2025-08-02 15:12:15.449 INFO log_util.__exit__ 135cfd50-1bd0-41b1-8126-3a498b7a1ede enter html_report cost=[1 ms]
2025-08-02 15:12:15.450 INFO log_util.__aenter__ 135cfd50-1bd0-41b1-8126-3a498b7a1ede  download_all_files start...
2025-08-02 15:12:15.451 INFO log_util.__aexit__ 135cfd50-1bd0-41b1-8126-3a498b7a1ede  download_all_files cost=[1 ms]
2025-08-02 15:12:15.451 INFO log_util.__enter__ 135cfd50-1bd0-41b1-8126-3a498b7a1ede  truncate_files start...
2025-08-02 15:12:15.452 INFO log_util.__exit__ 135cfd50-1bd0-41b1-8126-3a498b7a1ede  truncate_files cost=[0 ms]
2025-08-02 15:12:15.453 INFO log_util.__enter__ 135cfd50-1bd0-41b1-8126-3a498b7a1ede  truncate_files start...
2025-08-02 15:12:15.453 INFO log_util.__exit__ 135cfd50-1bd0-41b1-8126-3a498b7a1ede  truncate_files cost=[0 ms]
2025-08-02 15:12:15.455 INFO log_util.__aexit__ 135cfd50-1bd0-41b1-8126-3a498b7a1ede POST /v1/tool/report cost=[11 ms]
2025-08-02 15:17:28.694 INFO log_util.__aenter__ 482539e0-73d1-4c1b-90c9-fe25ade90b0d POST /v1/tool/report start...
2025-08-02 15:17:28.695 INFO middleware_util.custom_route_handler 482539e0-73d1-4c1b-90c9-fe25ade90b0d POST /v1/tool/report body={"contentStream":true,"fileDescription":"Android技术栈发展方向、发展前景及与鸿蒙对比分析报告","fileName":"Android技术栈分析报告.html","fileNames":[],"fileType":"html","query":"android技术主要技术栈发展方向，发展前景，与鸿蒙对比\n","requestId":"geniesession-1754119038578-3459:1754119038594-8725","stream":true,"streamMode":{"mode":"token","token":10},"task":"基于搜索结果，生成一份关于Android技术栈的全面分析报告，包括：\n1. Android技术栈的主要发展方向\n2. Android的发展前景和市场趋势\n3. Android与鸿蒙系统的详细对比分析\n4. 技术差异、优缺点对比\n5. 未来发展趋势预测\n\n报告需要结构清晰，内容详实，包含最新的技术发展趋势和市场分析数据。"}
2025-08-02 15:17:28.696 INFO log_util.__enter__ 482539e0-73d1-4c1b-90c9-fe25ade90b0d enter report start...
2025-08-02 15:17:28.696 INFO log_util.__exit__ 482539e0-73d1-4c1b-90c9-fe25ade90b0d enter report cost=[0 ms]
2025-08-02 15:17:28.696 INFO log_util.__enter__ 482539e0-73d1-4c1b-90c9-fe25ade90b0d enter html_report start...
2025-08-02 15:17:28.697 INFO log_util.__exit__ 482539e0-73d1-4c1b-90c9-fe25ade90b0d enter html_report cost=[1 ms]
2025-08-02 15:17:28.697 INFO log_util.__aenter__ 482539e0-73d1-4c1b-90c9-fe25ade90b0d  download_all_files start...
2025-08-02 15:17:28.697 INFO log_util.__aexit__ 482539e0-73d1-4c1b-90c9-fe25ade90b0d  download_all_files cost=[0 ms]
2025-08-02 15:17:28.697 INFO log_util.__enter__ 482539e0-73d1-4c1b-90c9-fe25ade90b0d  truncate_files start...
2025-08-02 15:17:28.697 INFO log_util.__exit__ 482539e0-73d1-4c1b-90c9-fe25ade90b0d  truncate_files cost=[0 ms]
2025-08-02 15:17:28.698 INFO log_util.__enter__ 482539e0-73d1-4c1b-90c9-fe25ade90b0d  truncate_files start...
2025-08-02 15:17:28.698 INFO log_util.__exit__ 482539e0-73d1-4c1b-90c9-fe25ade90b0d  truncate_files cost=[0 ms]
2025-08-02 15:17:28.699 INFO log_util.__aexit__ 482539e0-73d1-4c1b-90c9-fe25ade90b0d POST /v1/tool/report cost=[5 ms]
2025-08-02 15:22:12.408 INFO log_util.__aenter__ 221a10cd-1757-4a36-a80d-01e83c2106d6 POST /v1/tool/report start...
2025-08-02 15:22:12.409 INFO middleware_util.custom_route_handler 221a10cd-1757-4a36-a80d-01e83c2106d6 POST /v1/tool/report body={"contentStream":true,"fileDescription":"Android技术栈发展方向、发展前景及与鸿蒙对比分析报告","fileName":"Android技术栈分析报告.html","fileNames":[],"fileType":"html","query":"android技术主要技术栈发展方向，发展前景，与鸿蒙对比\n","requestId":"geniesession-1754119038578-3459:1754119320312-138","stream":true,"streamMode":{"mode":"token","token":10},"task":"基于搜索结果，生成一份关于Android技术栈的全面分析报告，包括：\n1. Android技术栈的主要发展方向\n2. Android的发展前景和市场趋势\n3. Android与鸿蒙系统的详细对比分析\n4. 总结和建议\n\n报告需要包含以下内容：\n- Android技术栈的最新发展趋势和技术方向\n- 市场前景分析和就业机会\n- Android与鸿蒙在技术架构、生态系统、性能等方面的对比\n- 对开发者的建议和未来展望\n\n使用HTML格式输出，包含适当的标题、段落、列表等格式，确保内容清晰易读。"}
2025-08-02 15:22:12.410 INFO log_util.__enter__ 221a10cd-1757-4a36-a80d-01e83c2106d6 enter report start...
2025-08-02 15:22:12.411 INFO log_util.__exit__ 221a10cd-1757-4a36-a80d-01e83c2106d6 enter report cost=[1 ms]
2025-08-02 15:22:12.412 INFO log_util.__enter__ 221a10cd-1757-4a36-a80d-01e83c2106d6 enter html_report start...
2025-08-02 15:22:12.413 INFO log_util.__exit__ 221a10cd-1757-4a36-a80d-01e83c2106d6 enter html_report cost=[0 ms]
2025-08-02 15:22:12.413 INFO log_util.__aenter__ 221a10cd-1757-4a36-a80d-01e83c2106d6  download_all_files start...
2025-08-02 15:22:12.413 INFO log_util.__aexit__ 221a10cd-1757-4a36-a80d-01e83c2106d6  download_all_files cost=[0 ms]
2025-08-02 15:22:12.414 INFO log_util.__enter__ 221a10cd-1757-4a36-a80d-01e83c2106d6  truncate_files start...
2025-08-02 15:22:12.414 INFO log_util.__exit__ 221a10cd-1757-4a36-a80d-01e83c2106d6  truncate_files cost=[0 ms]
2025-08-02 15:22:12.414 INFO log_util.__enter__ 221a10cd-1757-4a36-a80d-01e83c2106d6  truncate_files start...
2025-08-02 15:22:12.415 INFO log_util.__exit__ 221a10cd-1757-4a36-a80d-01e83c2106d6  truncate_files cost=[1 ms]
2025-08-02 15:22:12.416 INFO log_util.__aexit__ 221a10cd-1757-4a36-a80d-01e83c2106d6 POST /v1/tool/report cost=[8 ms]
